@tailwind base;
@tailwind components;
@tailwind utilities;

body {
	font-family: Arial, Helvetica, sans-serif;
}

@layer utilities {
	.text-balance {
		text-wrap: balance;
	}

	/* Animation utilities - Initial state (hidden) */
	/* Hero section immediate animations - restore original working version */
	.animate-fade-in-up {
		animation: fadeInUp 0.8s ease-out forwards;
		opacity: 0;
		transform: translateY(30px);
	}

	.animate-scale-in {
		animation: scaleIn 0.6s ease-out forwards;
		opacity: 0;
		transform: scale(0.8);
	}

	.animate-slide-in-right {
		animation: slideInRight 0.7s ease-out forwards;
		opacity: 0;
		transform: translateX(60px);
	}

	/* Scroll-triggered animations using same system as hero */
	.animate-scroll-fade-up {
		opacity: 0;
		transform: translateY(40px);
		animation: none;
	}

	.animate-scroll-fade-up.animate-in {
		animation: fadeInUp 0.8s ease-out forwards;
	}

	.animate-scroll-slide-left {
		opacity: 0;
		transform: translateX(-60px);
		animation: none;
	}

	.animate-scroll-slide-left.animate-in {
		animation: slideInLeft 0.7s ease-out forwards;
	}

	.animate-scroll-slide-right {
		opacity: 0;
		transform: translateX(60px);
		animation: none;
	}

	.animate-scroll-slide-right.animate-in {
		animation: slideInRight 0.7s ease-out forwards;
	}

	.animate-scroll-scale {
		opacity: 0;
		transform: scale(0.9);
		animation: none;
	}

	.animate-scroll-scale.animate-in {
		animation: scaleIn 0.6s ease-out forwards;
	}

	/* Stagger delays for scroll animations */
	.animate-scroll-stagger-1.animate-in {
		animation-delay: 0.1s;
	}
	.animate-scroll-stagger-2.animate-in {
		animation-delay: 0.2s;
	}
	.animate-scroll-stagger-3.animate-in {
		animation-delay: 0.3s;
	}
	.animate-scroll-stagger-4.animate-in {
		animation-delay: 0.4s;
	}
	.animate-scroll-stagger-5.animate-in {
		animation-delay: 0.5s;
	}

	/* Ensure hover effects work after animations complete */
	.animate-scroll-fade-up.animate-in,
	.animate-scroll-slide-left.animate-in,
	.animate-scroll-slide-right.animate-in,
	.animate-scroll-scale.animate-in {
		animation-fill-mode: forwards;
	}

	/* Fix hover effects for cards after animations */
	.animate-scroll-fade-up.animate-in:hover,
	.animate-scroll-slide-left.animate-in:hover,
	.animate-scroll-slide-right.animate-in:hover,
	.animate-scroll-scale.animate-in:hover {
		transform: scale(1.02) !important;
		transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	}

	/* Hero section stagger (immediate) */
	.animate-stagger-1 {
		animation-delay: 0.1s;
	}
	.animate-stagger-2 {
		animation-delay: 0.2s;
	}
	.animate-stagger-3 {
		animation-delay: 0.3s;
	}
	.animate-stagger-4 {
		animation-delay: 0.4s;
	}
	.animate-stagger-5 {
		animation-delay: 0.5s;
	}
}

/* Keyframe animations */
@keyframes fadeInUp {
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes fadeIn {
	to {
		opacity: 1;
	}
}

@keyframes slideInLeft {
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes slideInRight {
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes scaleIn {
	to {
		opacity: 1;
		transform: scale(1);
	}
}



/* All scroll-triggered animations now use animate-on-scroll containers with transitions */

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
	.animate-fade-in-up,
	.animate-fade-in,
	.animate-slide-in-left,
	.animate-slide-in-right,
	.animate-scale-in,
	.animate-on-scroll {
		animation: none;
		opacity: 1;
		transform: none;
	}
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 0 0% 3.9%;
		--card: 0 0% 100%;
		--card-foreground: 0 0% 3.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 0 0% 3.9%;
		--primary: 0 0% 9%;
		--primary-foreground: 0 0% 98%;
		--secondary: 0 0% 96.1%;
		--secondary-foreground: 0 0% 9%;
		--muted: 0 0% 96.1%;
		--muted-foreground: 0 0% 45.1%;
		--accent: 0 0% 96.1%;
		--accent-foreground: 0 0% 9%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 0 0% 89.8%;
		--input: 0 0% 89.8%;
		--ring: 0 0% 3.9%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--radius: 0.5rem;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 240 5.3% 26.1%;
		--sidebar-primary: 240 5.9% 10%;
		--sidebar-primary-foreground: 0 0% 98%;
		--sidebar-accent: 240 4.8% 95.9%;
		--sidebar-accent-foreground: 240 5.9% 10%;
		--sidebar-border: 220 13% 91%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
	.dark {
		--background: 0 0% 3.9%;
		--foreground: 0 0% 98%;
		--card: 0 0% 3.9%;
		--card-foreground: 0 0% 98%;
		--popover: 0 0% 3.9%;
		--popover-foreground: 0 0% 98%;
		--primary: 0 0% 98%;
		--primary-foreground: 0 0% 9%;
		--secondary: 0 0% 14.9%;
		--secondary-foreground: 0 0% 98%;
		--muted: 0 0% 14.9%;
		--muted-foreground: 0 0% 63.9%;
		--accent: 0 0% 14.9%;
		--accent-foreground: 0 0% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;
		--border: 0 0% 14.9%;
		--input: 0 0% 14.9%;
		--ring: 0 0% 83.1%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
		--sidebar-background: 240 5.9% 10%;
		--sidebar-foreground: 240 4.8% 95.9%;
		--sidebar-primary: 224.3 76.3% 48%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 240 3.7% 15.9%;
		--sidebar-accent-foreground: 240 4.8% 95.9%;
		--sidebar-border: 240 3.7% 15.9%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}
