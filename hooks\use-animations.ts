import { useReducedMotion } from "framer-motion";

// Animation variants for different types of elements
export const fadeInVariants = {
  hidden: { 
    opacity: 0,
    y: 20
  },
  visible: { 
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const slideUpVariants = {
  hidden: { 
    opacity: 0,
    y: 60
  },
  visible: { 
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.7,
      ease: "easeOut"
    }
  }
};

export const slideLeftVariants = {
  hidden: { 
    opacity: 0,
    x: -60
  },
  visible: { 
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.7,
      ease: "easeOut"
    }
  }
};

export const slideRightVariants = {
  hidden: { 
    opacity: 0,
    x: 60
  },
  visible: { 
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.7,
      ease: "easeOut"
    }
  }
};

export const scaleVariants = {
  hidden: { 
    opacity: 0,
    scale: 0.8
  },
  visible: { 
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

export const staggerContainerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

export const staggerItemVariants = {
  hidden: { 
    opacity: 0,
    y: 30
  },
  visible: { 
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
};

// Hover animations for interactive elements
export const hoverScaleVariants = {
  hover: { 
    scale: 1.05,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  },
  tap: { 
    scale: 0.95,
    transition: {
      duration: 0.1
    }
  }
};

export const cardHoverVariants = {
  hover: { 
    y: -8,
    scale: 1.02,
    transition: {
      duration: 0.3,
      ease: "easeInOut"
    }
  }
};

// Viewport options for intersection observer
export const viewportOptions = {
  once: true,
  margin: "-50px",
  amount: 0.3
};

export const mobileViewportOptions = {
  once: true,
  margin: "-20px",
  amount: 0.2
};

// Hook to get appropriate viewport options based on screen size
export const useViewportOptions = () => {
  const shouldReduceMotion = useReducedMotion();
  
  if (shouldReduceMotion) {
    return {
      once: true,
      margin: "0px",
      amount: 0
    };
  }

  // Check if mobile (simplified approach)
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  
  return isMobile ? mobileViewportOptions : viewportOptions;
};

// Reduced motion variants
export const getReducedMotionVariants = (shouldReduceMotion: boolean) => {
  if (shouldReduceMotion) {
    return {
      hidden: { opacity: 0 },
      visible: { 
        opacity: 1,
        transition: { duration: 0.1 }
      }
    };
  }
  return fadeInVariants;
};
