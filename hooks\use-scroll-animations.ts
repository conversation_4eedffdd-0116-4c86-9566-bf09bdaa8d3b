"use client";

import { useEffect } from "react";

export const useScrollAnimations = () => {
	useEffect(() => {
		// Check if user prefers reduced motion
		const prefersReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches;

		if (prefersReducedMotion) {
			// If reduced motion is preferred, show all elements immediately
			const allAnimateElements = document.querySelectorAll(
				".animate-on-scroll, .animate-fade-in-up, .animate-slide-in-left, .animate-slide-in-right, .animate-scale-in"
			);
			allAnimateElements.forEach((el) => {
				el.classList.add("animate-in");
			});
			return;
		}

		// Create intersection observer for scroll-triggered animations
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						console.log("Animating section:", entry.target.id);
						entry.target.classList.add("animate-in");

						// Also trigger animations for child elements with stagger classes
						const staggerElements = entry.target.querySelectorAll('[class*="animate-stagger"]');
						staggerElements.forEach((child) => {
							child.classList.add("animate-in");
						});
					}
				});
			},
			{
				threshold: 0.3,
				rootMargin: "0px 0px -100px 0px",
			}
		);

		// Initialize animations when DOM is ready
		const initializeAnimations = () => {
			// Observe all elements with animate-on-scroll class
			const animateOnScrollElements = document.querySelectorAll(".animate-on-scroll");

			animateOnScrollElements.forEach((el) => {
				observer.observe(el);
			});
		};

		// Use a small delay to ensure DOM is fully rendered
		const timer = setTimeout(() => {
			initializeAnimations();
		}, 100);

		// Cleanup function
		return () => {
			clearTimeout(timer);
			observer.disconnect();
		};
	}, []);
};
