"use client";

import { useEffect } from "react";

export const useScrollAnimations = () => {
	useEffect(() => {
		// Check if user prefers reduced motion
		const prefersReducedMotion = window.matchMedia("(prefers-reduced-motion: reduce)").matches;

		if (prefersReducedMotion) {
			// If reduced motion is preferred, show all elements immediately
			const allAnimateElements = document.querySelectorAll(".animate-on-scroll");
			allAnimateElements.forEach((el) => {
				el.classList.add("animate-in");
			});
			return;
		}

		// Simple intersection observer
		const observer = new IntersectionObserver(
			(entries) => {
				entries.forEach((entry) => {
					if (entry.isIntersecting) {
						entry.target.classList.add("animate-in");
					}
				});
			},
			{
				threshold: 0.1,
				rootMargin: "0px 0px -50px 0px",
			}
		);

		// Observe all sections
		const sections = document.querySelectorAll(".animate-on-scroll");
		sections.forEach((section) => observer.observe(section));

		// Cleanup
		return () => observer.disconnect();
	}, []);
};
